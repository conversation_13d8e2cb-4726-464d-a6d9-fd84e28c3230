import axios from "axios";
import { errorAlert } from "@/lib/alert";

const handleAxiosError = (error: unknown): void => {
  if (axios.isAxiosError(error)) {
    if (error.code === "ERR_CANCELED") {
      // Do not log canceled requests
      return;
    }

    if (error.response) {
      // Server responded with a status other than 200 range
      const errorMessage =
        (error.response.data as { result?: { message: string } })?.result?.message ||
        "An error occurred";
      errorAlert(errorMessage);
    } else if (error.request) {
      // Request was made but no response was received
      errorAlert("Request failed. Please try again later.");
    } else {
      // Something happened in setting up the request
    }
  } else {
    // Handle non-Axios errors
  }
};

export default handleAxiosError;
