import handleAxiosError from "@/lib/axiosErrorHandle";
import { privateClient } from "@/utils/privateClient";

export async function getAllWorkflow(): Promise<WorkflowResponse> {
  try {
    const response = await privateClient.get("/workflows/");
    const { data, status } = response;

    if (status) {
      return data;
    } else {
      return {
        workflows: [],
        total: 0,
        page: 1,
        size: 100,
      } as WorkflowResponse; // Default empty response
    }
  } catch (error) {
    handleAxiosError(error);
    return { workflows: [], total: 0, page: 1, size: 100 } as WorkflowResponse; // Default empty response
  }
}
export async function getAddWorkflow(): Promise<Workflow | []> {
  try {
    const response = await privateClient.get("/workflows/new");
    const { data, status } = response;

    if (status) {
      return data;
    } else {
      return [];
    }
  } catch (error) {
    handleAxiosError(error);
    return [];
  }
}

export async function createWorkflow(body: Record<string, unknown>): Promise<unknown> {
  try {
    const response = await privateClient.post("/workflows/", body);
    const { data, status } = response;

    if (status) {
      return data;
    } else {
      return [];
    }
  } catch (error) {
    handleAxiosError(error);
    return [];
  }
}


export async function getWorkflowById(workflowId: string): Promise<Workflow | null> {
  try {
    const response = await privateClient.get(`/workflows/${workflowId}`);
    const { data, status } = response;

    if (status) {
      return data;
    } else {
      return null;
    }
  } catch (error) {
    handleAxiosError(error);
    return null;
  }
}

export async function executeWorkflow(
  workflowId: string,
  workflowData: {
    work_flow: {
      nodes: Record<string, any>;
      connections: Record<string, any>;
    };
    input_data: Record<string, any>;
  },
): Promise<any> {
  try {
    const response = await privateClient.post(`/workflows/execute/${workflowId}`, workflowData);
    const { data, status } = response;

    if (status || response.status === 200) {
      return data;
    } else {
      throw new Error("Failed to execute workflow");
    }
  } catch (error) {
    handleAxiosError(error);
    throw error;
  }
}

export async function executeSaveWorkflow(
  workflowId: string,
  workflowData: {
    work_flow: {
      nodes: Record<string, any>;
      connections: Record<string, any>;
    };
    input_data: Record<string, any>;
  },
): Promise<any> {
  try {
    const response = await privateClient.patch(`/workflows/${workflowId}`, workflowData);
    const { data, status } = response;

    if (status || response.status === 200) {
      return data;
    } else {
      throw new Error("Failed to execute workflow");
    }
  } catch (error) {
    handleAxiosError(error);
    throw error;
  }
}
