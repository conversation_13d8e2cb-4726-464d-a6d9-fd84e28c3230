import handleAxiosError from "@/lib/axiosErrorHandle";
import { useCredentialsStore, type CreateCredentialConfig } from "@/stores/credentialStore";
import { privateClient } from "@/utils/privateClient";

export async function createCredential(credentialData: CreateCredentialConfig): Promise<any> {
  try {
    const response = await privateClient.post("/credentials/", credentialData);

    const { data, status } = response;

    if (status) {
      return {
        ...data.credential,
        parameters: data.parameters ?? credentialData.parameters,
      };
    } else {
      throw new Error("Failed to create credential");
    }
  } catch (error) {
    handleAxiosError(error);
    throw error;
  }
}

// api for get credential id
export async function getCredentialById(credentialId: string): Promise<any> {
  try {
    const response = await privateClient.get(`/credentials/${credentialId}`);
    const { data, status } = response;

    if (status === 200) {
      return data;
    } else {
      throw new Error("Failed to fetch credential");
    }
  } catch (error) {
    handleAxiosError(error);
    return [];
  }
}

// get saved credentials
export async function getSavedCredentials(credentialType: string): Promise<any[]> {
  try {
    const response = await privateClient.get(`/credentials/type/${credentialType}`);
    const { data, status } = response;

    if (status) {
      // Save to store with credentialType as key
      const store = useCredentialsStore.getState();
      store.setSavedCredentialsByType?.(credentialType, data);

      return data;
    } else {
      return [];
    }
  } catch (error) {
    handleAxiosError(error);
    return [];
  }
}

//test credential
export async function testCredentialById(credentialId: string): Promise<any> {
  try {
    const response = await privateClient.get(`/credentials/test/${credentialId}`);
    return response.data;
  } catch (error) {
    handleAxiosError(error);
    throw error;
  }
}

// update credential
export async function updateCredentialById(
  credentialId: string,
  credentialData: CreateCredentialConfig,
): Promise<any> {
  try {
    const response = await privateClient.patch(`/credentials/${credentialId}`, credentialData);
    return response.data;
  } catch (error) {
    handleAxiosError(error);
    throw error;
  }
}

//Oauth credential
export async function oauthCredential(credentialData: CreateCredentialConfig): Promise<any> {
  try {
    const response = await privateClient.post(`/credentials/authorize`, credentialData);
    return response.data;
  } catch (error) {
    handleAxiosError(error);
    throw error;
  }
}

//Update Oauth credential
export async function editOAuthCredentialById(
  credentialId: string,
  credentialData: CreateCredentialConfig,
): Promise<any> {
  try {
    const response = await privateClient.patch(
      `/credentials/authorize/${credentialId}`,
      credentialData,
    );
    return response.data;
  } catch (error) {
    handleAxiosError(error);
    throw error;
  }
}
