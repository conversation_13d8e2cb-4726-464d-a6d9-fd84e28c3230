import handleAxiosError from "@/lib/axiosErrorHandle";
import { privateClient } from "@/utils/privateClient";

export interface NodeParameter {
  name: string;
  type: string;
  default?: unknown;
  display_name: string;
  description: string;
  required: boolean;
  placeholder?: string;
  depends_on?: string[];
  options?: Array<{ name: string; value: string; description?: string }>;
  display_options?: {
    show?: { [key: string]: string[] };
  };
  type_options?: {
    min_value?: number;
    max_value?: number;
    multiple_values?: boolean;
    multiple_value_button_text?: string;
    number_precision?: number;
    load_options_depends_on?: string[];
    load_options?: {
      function: string;
    };
  };
}

export interface NodeType {
  name: string;
  display_name: string;
  description: string;
  icon: string;
  icon_color: string;
  group: string[];
  version: number;
  parameters: NodeParameter[];
  inputs: string[];
  outputs: string[];
  output_names?: string[];
  input_names?: string[];
  hidden: boolean;
  credentials?: Array<{
    name: string;
    display_name: string;
    required: boolean;
  }>;
}

export interface NodeData {
  id: string;
  type: string;
  label: string;
  parameters: { [key: string]: unknown };
  nodeType: NodeType;
  executionData?: {
    startTime?: number;
    endTime?: number;
    status?: "idle" | "running" | "success" | "error";
    error?: string;
    output?: unknown;
  };
  inputData?: unknown; // JSON input data for first nodes
}

export async function getNodeOptions(payload: {
  node_request: {
    name: string;
    type: string;
    display_properties: Record<string, unknown>;
    is_active: boolean;
    is_trigger: boolean;
    parameters: Record<string, unknown>;
    credentials: Record<string, { id: string; name: string }>;
  };
  function: string;
}): Promise<any[]> {
  try {
    const response = await privateClient.post("/nodes/options", payload);
    const { data, status } = response;

    if (status) {
      return data;
    } else {
      return [];
    }
  } catch (error) {
    handleAxiosError(error);
    return [];
  }
}
