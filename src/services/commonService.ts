import { privateClient } from "@/utils/privateClient";
import type { NodeType } from "./nodeService";
import handleAxiosError from "@/lib/axiosErrorHandle";

export async function getNodeTypes(): Promise<NodeType[]> {
  try {
    const response = await privateClient.get("/nodes/");
    const { data, status } = response;
    if (status) {
      return data;
    } else {
      return [];
    }
  } catch (error) {
    handleAxiosError(error);
    return [];
  }
}

export async function getCredentials(): Promise<any[]> {
  try {
    const response = await privateClient.get("/credentials/");
    const { data, status } = response;

    if (status) {
      return data;
    } else {
      return [];
    }
  } catch (error) {
    handleAxiosError(error);
    return [];
  }
}
