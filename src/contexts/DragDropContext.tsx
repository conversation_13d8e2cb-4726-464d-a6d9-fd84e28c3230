import React, { createContext, useContext, useState, type ReactNode } from "react";

export interface DragData {
  type: "node-data" | "expression";
  nodeId: string;
  nodeName: string;
  dataPath: string;
  value: unknown;
  displayName: string;
}

export interface NodeDataForEvaluation {
  id: string;
  label?: string;
  nodeType: {
    display_name: string;
  };
  executionData?: {
    output?: unknown;
  };
}

export interface DragDropContextType {
  dragData: DragData | null;
  setDragData: (data: DragData | null) => void;
  isDragging: boolean;
  setIsDragging: (dragging: boolean) => void;
  dropTarget: string | null;
  setDropTarget: (target: string | null) => void;
  availableNodes: NodeDataForEvaluation[];
  setAvailableNodes: (nodes: NodeDataForEvaluation[]) => void;
}

const DragDropContext = createContext<DragDropContextType | undefined>(undefined);

// eslint-disable-next-line react-refresh/only-export-components
export const useDragDrop = () => {
  const context = useContext(DragDropContext);
  if (!context) {
    throw new Error("useDragDrop must be used within a DragDropProvider");
  }
  return context;
};

interface DragDropProviderProps {
  children: ReactNode;
}

export const DragDropProvider: React.FC<DragDropProviderProps> = ({ children }) => {
  const [dragData, setDragData] = useState<DragData | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dropTarget, setDropTarget] = useState<string | null>(null);
  const [availableNodes, setAvailableNodes] = useState<NodeDataForEvaluation[]>([]);

  const value: DragDropContextType = {
    dragData,
    setDragData,
    isDragging,
    setIsDragging,
    dropTarget,
    setDropTarget,
    availableNodes,
    setAvailableNodes,
  };

  return <DragDropContext.Provider value={value}>{children}</DragDropContext.Provider>;
};
