import LogoBar from "@/components/menu/LogoBar";
import SideBar from "@/components/menu/SideBar";
import TopBar from "@/components/menu/TopBar";
import { useUploadStore } from "@/stores/useUploadstore";

interface LayoutProps {
    children: React.ReactNode;
    showTopBar?: boolean;
    showSideBar?: boolean;
}

export default function Layout({
    children,
    showTopBar = false,
    showSideBar = false,
}: LayoutProps) {
    const { mode } = useUploadStore();
    /// force-hidebars
    const shouldShowTopBar =
        mode === "bulk" ? false : mode === "attribute" ? false : showTopBar;

    const shouldShowSideBar =
        mode === "bulk" ? false : mode === "attribute" ? false : showSideBar;

    return (
        <main className="h-full bg-black text-white">
            <LogoBar />

            {shouldShowTopBar && (
                <div className="mt-3.5">
                    <TopBar />
                </div>
            )}

            <div className="flex">
                {shouldShowSideBar && <SideBar />}

                <section
                    className={`w-full min-h-screen overflow-auto relative 
            ${shouldShowSideBar ? "md:w-[calc(100vw-100px)] md:h-[calc(100vh-100px)]" : ""}`}
                >
                    {children}
                </section>
            </div>
        </main>
    );
}
