.corner-gradient {
    position: relative;
    overflow: hidden; /* clip inside */
  }
  
  .corner-gradient::before,
  .corner-gradient::after {
    content: "";
    position: absolute;
    width: 60px; /* same size as your .size-60 */
    height: 60px;
    pointer-events: none;
    background: linear-gradient(
      224deg,
      rgba(201, 211, 238, 0),
      #C9D3EE,
      rgba(201, 211, 238, 0)
    ) border-box;
    border-color: transparent;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
  }
  
  /* Top-left corner */
  .corner-gradient::before {
    top: 0;
    left: 0;
    border-top: 2px solid transparent;
    border-left: 2px solid transparent;
    border-top-left-radius: 1.5rem; /* match rounded-tl-3xl */
  }
  
  /* Bottom-right corner */
  .corner-gradient::after {
    bottom: 0;
    right: 0;
    border-bottom: 2px solid transparent;
    border-right: 2px solid transparent;
    border-bottom-right-radius: 1.5rem; /* match rounded-br-3xl */
  }
  