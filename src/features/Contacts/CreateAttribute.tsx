import * as React from "react";
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    DialogTitle,
    DialogDescription,
    DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";

interface CreateAttributeProps {
    open: boolean;               // instead of isOpen
    onClose: () => void;
    onSave: (data: { name: string; defaultValue: string; type: string }) => void;
}

export default function CreateAttribute({ open, onClose, onSave }: CreateAttributeProps) {
    const [name, setName] = React.useState("");
    const [defaultValue, setDefaultValue] = React.useState("");
    const [type, setType] = React.useState("");

    const handleSubmit = () => {
        onSave({ name, defaultValue, type });
        setName("");
        setDefaultValue("");
        setType("");
        onClose();
    };

    return (
        <Dialog open={open} onOpenChange={onClose}>

            <DialogContent className="sm:max-w-md bg-[#1a1a1a] text-white border border-violet-700">
                <DialogHeader>
                    <DialogTitle>Create Attribute</DialogTitle>
                    <DialogDescription className="text-gray-400">
                        Add a new attribute by filling the details below.
                    </DialogDescription>
                </DialogHeader>

                <div className="space-y-4">
                    {/* Attribute Name */}
                    <div className="flex flex-col space-y-1">
                        <Label htmlFor="name">Attribute Name</Label>
                        <Input
                            id="name"
                            placeholder="Enter Name"
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                            className="border-violet-500 bg-transparent text-white"
                        />
                    </div>

                    {/* Default Value */}
                    <div className="flex flex-col space-y-1">
                        <Label htmlFor="default">Default Value</Label>
                        <Input
                            id="default"
                            placeholder="Enter Value"
                            value={defaultValue}
                            onChange={(e) => setDefaultValue(e.target.value)}
                            className="border-violet-500 bg-transparent text-white"
                        />
                    </div>

                    {/* Data Type */}
                    <div className="flex flex-col space-y-1">
                        <Label htmlFor="type">Data Type</Label>
                        <Select value={type} onValueChange={(v) => setType(v)}>
                            <SelectTrigger className="border-violet-500 bg-transparent text-white">
                                <SelectValue placeholder="Select Type" />
                            </SelectTrigger>
                            <SelectContent className="bg-[#1a1a1a] text-white border-violet-700">
                                <SelectItem value="string">String</SelectItem>
                                <SelectItem value="integer">Integer</SelectItem>
                                <SelectItem value="uuid">UUID</SelectItem>
                                <SelectItem value="boolean">Boolean</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <DialogFooter>
                    <Button
                        onClick={handleSubmit}
                        className="bg-violet-600 hover:bg-violet-700 w-full"
                    >
                        Create
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}
