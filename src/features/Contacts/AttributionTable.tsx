import { EllipsisVertical, Search } from "lucide-react";
import Stepper from "./Stepper";

import { useNavigate } from "react-router";
import { useState } from "react";
import CreateAttribute from "./CreateAttribute";


interface Attribute {
    name: string;
    id: number;
    createdAt: string;
    type: string;
}



const attributes: Attribute[] = [
    { name: "UUID", id: 287, createdAt: "Jun 24, 2025 12:19 PM", type: "uuid" },
    { name: "firstname", id: 287, createdAt: "Jun 24, 2025 12:19 PM", type: "string" },
    { name: "last name", id: 287, createdAt: "Jun 24, 2025 12:19 PM", type: "string" },
    { name: "email", id: 287, createdAt: "Jun 24, 2025 12:19 PM", type: "string" },
    { name: "phone", id: 287, createdAt: "Jun 24, 2025 12:19 PM", type: "integer" },
    { name: "xxx", id: 287, createdAt: "Jun 24, 2025 12:19 PM", type: "textline" },
    { name: "rrr", id: 287, createdAt: "Jun 24, 2025 12:19 PM", type: "textline" },
    { name: "ddd", id: 287, createdAt: "Jun 24, 2025 12:19 PM", type: "textline" },
    { name: "zzzzz", id: 287, createdAt: "Jun 24, 2025 12:19 PM", type: "textline" },
    { name: "jjjj", id: 287, createdAt: "Jun 24, 2025 12:19 PM", type: "textline" },
];

export default function AttributionTable() {

    const navigate = useNavigate();
    const [openDropdown, setOpenDropdown] = useState<number | null>(null);

    const [isModalOpen, setIsModalOpen] = useState(false);

    return (
        <div className="p-4 space-y-6">
            <Stepper
                step={1}
                onBack={() => navigate("/contact")}
                title="Attributes"
                subtitle="View and manage your attributes"
                rightContent={
                    <div className="flex items-center gap-3">
                        {/* Search */}
                        <button className="flex items-center gap-1 bg-[#232323]  text-white px-3 py-2 rounded-lg text-sm font-medium">
                            <Search size={18} />

                        </button>
                        {/* Logs */}
                        <button className="flex items-center gap-1 bg-[#232323]  text-white px-6 py-2 rounded-lg text-sm font-medium">
                            Atrributes Logs
                        </button>
                        {/* Create Attribute */}
                        <button
                            onClick={() => setIsModalOpen(true)} // 👈 open modal
                            className="flex items-center gap-1 bg-[#232323] text-white px-6 py-2 rounded-lg text-sm font-medium"
                        >
                            Create New
                        </button>


                    </div>
                }


            />
            <div className="overflow-hidden rounded-xl border border-[#353535] bg-[#232323] shadow">
                <table className="w-full border-collapse text-sm text-gray-300">
                    <thead>
                        <tr className=" text-left  text-xs uppercase tracking-wider">
                            <th className="px-4 py-3 border border-[#353535]">Attribute name</th>
                            <th className="px-4 py-3 border border-[#353535]">Created at</th>
                            <th className="px-4 py-3 border border-[#353535]">Data type</th>
                            <th className="px-4 py-3 border border-[#353535] w-[60px]">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        {attributes.map((attr, idx) => (
                            <tr key={idx} className="transition-colors">
                                <td className="px-4 py-3 border border-[#353535]">
                                    <div className="flex flex-col">
                                        <span className="font-medium text-white">{attr.name}</span>
                                        <span className="text-xs text-gray-500">ID - {attr.id}</span>
                                    </div>
                                </td>
                                <td className="px-4 py-3 border border-[#353535]">{attr.createdAt}</td>
                                <td className="px-4 py-3 border border-[#353535]">{attr.type}</td>
                                <td className="px-2 py-3 border border-[#353535] w-[60px] relative">
                                    <button
                                        onClick={() => setOpenDropdown(openDropdown === idx ? null : idx)}
                                        className="p-2 rounded hover:bg-[#2e2e2e]"
                                    >
                                        <EllipsisVertical className="w-5 h-5 text-gray-400" />
                                    </button>

                                    {/* Dropdown */}
                                    {openDropdown === idx && (
                                        <div className="absolute right-2 top-10 z-10 bg-[#2a2a2a] border border-[#353535] rounded-lg shadow-lg w-32">
                                            <button
                                                className="block w-full text-left px-4 py-2 text-sm text-white hover:bg-[#3a3a3a]"
                                                onClick={() => {

                                                    setOpenDropdown(null);
                                                }}
                                            >
                                                Edit
                                            </button>
                                            <button
                                                className="block w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-[#3a3a3a]"
                                                onClick={() => {

                                                    setOpenDropdown(null);
                                                }}
                                            >
                                                Delete
                                            </button>
                                        </div>
                                    )}
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>

            </div>
            <CreateAttribute
                open={isModalOpen}   // ✅ instead of open
                onClose={() => setIsModalOpen(false)}
                onSave={(data) => {
                    // console.log("New Attribute:", data);
                }}
            />
        </div>
    );
}
