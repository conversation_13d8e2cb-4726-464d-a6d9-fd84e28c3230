export default function UploadStatus({ onDone }: { onDone: () => void }) {
    return (
        <div className="text-white">
            <h2 className="text-2xl font-bold">Status</h2>
            <p className="text-white/70 mb-6">check status and add more contacts</p>

            <div className="bg-[#232323] rounded-lg p-3 flex justify-between items-center w-[60%]">
                <div>
                    <p className="font-semibold text-[14px]">Upload request successful</p>
                    <p className="text-xs ">Upload request ID: 5684605</p>
                    <p className="text-xs ">Instance/Panel name: mileseducationce</p>
                    <p className="text-xs ">segment name: unnamed</p>
                </div>
                <div className="flex gap-4">
                    <div className="text-center">
                        <p className="text-xs ">total unique contacts</p>
                        <p className="text-lg font-bold">03</p>

                    </div>
                    <div className="text-center">
                        <p className="text-xs ">unique email IDs</p>
                        <p className="text-lg font-bold">304</p>

                    </div>
                    <div className="text-center">
                        <p className="text-xs ">unique phone number</p>
                        <p className="text-lg font-bold">304</p>

                    </div>
                </div>
            </div>

            <div className="bg-[#232323] rounded-lg p-3 mt-6  w-[70%]">
                <p className="font-semibold text-[16px] mb-2">Note:</p>
                <ul className="list-disc list-inside space-y-1 text-sm font-[400px]">
                    <li>Small upload (&lt;100 records) → ~5 min to process</li>
                    <li>Large upload (10 lakh records) → ~10 min to process</li>
                    <li>Use request ID, filename, or panel name for support queries</li>
                    <li>You will be notified by email about request status</li>
                </ul>
            </div>

            <div className="fixed bottom-36 right-6 z-50">
                <button
                    className="px-7 py-2 w-40 bg-[#5858B8] border-2 border-[#53498c] text-white rounded-lg"
                    onClick={onDone}
                >
                    Done
                </button>
            </div>
        </div>
    );
}
