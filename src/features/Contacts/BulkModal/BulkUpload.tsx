import { useState } from "react";
import UploadIcon from "../../../assets/UploadIcon.svg";
import Stepper from "../Stepper";
import UploadFile from "./UploadFile";
import MapAttributions from "./MapAttribution";
import StatusStep from "./StatusStep";
import { useUploadStore } from "@/stores/useUploadstore";
import { BarChart, MapPin, Upload } from "lucide-react";
import { useNavigate } from "react-router-dom";

export default function BulkUpload() {
    const [step, setStep] = useState(1);
    const { reset } = useUploadStore();
    const navigate = useNavigate();

    return (
        <div className="min-h-screen">
            {/* Stepper always visible */}
            <Stepper
                step={2}
                onBack={() => navigate("/contact")}
                title="Bulk Upload"
                subtitle="Some sub heading here."
                steps={[
                    { icon: <Upload size={18} className="text-white" />, label: "Upload file" },
                    { icon: <MapPin size={18} className="text-white" />, label: "Map attribution" },
                    { icon: <BarChart size={18} className="text-white" />, label: "Status" },
                ]}
            />


            <div className="relative z-10 max-w-6xl mx-auto px-6 py-8">
                {step === 1 && (
                    <div className="flex flex-col items-center">
                        <div className="p-10 text-center w-full max-w-md">
                            <img src={UploadIcon} alt="Upload" className="mx-auto mb-6 w-20 h-20" />
                            <p className="text-white text-lg">
                                Drag your file here or{" "}
                                <span className="text-blue-400 cursor-pointer hover:underline">browse</span>
                            </p>
                            <p className="text-sm text-white/50 mt-2">
                                Formats CSV, ZIP, and TXT with maximum file size of 150MB
                            </p>
                            <p className="text-xs text-white/40 mt-1">
                                To ensure proper handling of special or multilingual characters, it is recommended to
                                upload a UTF-8 enclosed CSV file.{" "}
                                <span className="text-blue-400 hover:underline cursor-pointer">Know more</span>
                            </p>

                            <button className="mt-6 px-5 py-2 bg-[#5858B8] border-2 border-[#53498c] text-white rounded-lg shadow-md">
                                Download sample file
                            </button>
                        </div>

                        <div className="flex justify-end w-full mt-10">
                            <button
                                className="px-7 py-2 w-40 bg-[#5858B8] border-2 border-[#53498c] text-white rounded-lg"
                                onClick={() => setStep(2)}
                            >
                                Next
                            </button>
                        </div>
                    </div>
                )}

                {step === 2 && <UploadFile onNext={() => setStep(3)} />}
                {step === 3 && <MapAttributions onNext={() => setStep(4)} />}
                {step === 4 && (
                    <StatusStep
                        onDone={() => {
                            reset();
                        }}
                    />
                )}
            </div>
        </div>
    );
}
