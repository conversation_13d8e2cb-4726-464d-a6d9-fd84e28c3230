import { Trash } from "lucide-react";
import { Switch } from "@/components/ui/switch";

interface UploadFileProps {
    onNext: () => void;
}

export default function UploadFile({ onNext }: UploadFileProps) {
    return (
        <>
            {/* Content (top-left) */}
            <div className="w-full max-w-[600px] p-6">
                <h2 className="text-white text-2xl font-semibold">Upload file</h2>
                <p className="text-white/60 text-lg font-normal">
                    Drop the file containing your contacts
                </p>

                {/* File row */}
                <div className="mt-4 bg-[#232323] rounded-md p-4 flex items-center justify-between">
                    <span className="text-white/80">File name here</span>
                    <div className="flex gap-2">
                        <button className="bg-[#4B4B4B] px-3 py-1 rounded text-white/80">Replace</button>
                        <button className="bg-[#4B4B4B] px-2 py-2 rounded text-white/80">
                            <Trash size={16} />
                        </button>
                    </div>
                </div>

                {/* Fix typos */}
                <div className="mt-3 bg-[#232323] rounded-md p-4 flex items-center justify-between">
                    <div>
                        <p className="text-white">Fix typos</p>
                        <p className="text-white/60 text-sm">
                            Some email addresses might have typographical errors.
                        </p>
                    </div>
                    <Switch />
                </div>

                {/* Add to list */}
                <div className="mt-3 bg-[#232323] rounded-md p-4 flex items-center justify-between">
                    <p className="text-white">Add to list</p>
                    <Switch />
                </div>
            </div>

            {/* Next button (bottom-right of the page) */}
            {/* Next button (slightly above bottom-right) */}
            <div className="fixed bottom-36 right-6 z-50">
                <button
                    onClick={onNext}
                    className="px-7 py-2 w-40 bg-[#5858B8] border-2 border-[#53498c] text-white rounded-lg"
                >
                    Next
                </button>
            </div>

        </>
    );
}
