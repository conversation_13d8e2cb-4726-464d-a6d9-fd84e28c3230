import { ChevronDown } from "lucide-react";

interface MapAttributionsProps {
    onNext: () => void;
}

export default function MapAttributions({ onNext }: MapAttributionsProps) {
    return (
        <div className="w-full max-w-6xl text-white ">
            {/* Heading */}
            <h2 className="text-2xl font-semibold mb-2">Map Attributions</h2>
            <p className="text-white/60 text-xl font-[400px]">Great, we have found total 4 records.</p>

            {/* Step indicators with text */}
            <div className="flex items-center gap-3 mb-6">
                <span className="text-lg font-semibold text-white">Fields mapped (3/4)</span>
                <div className="flex gap-2">
                    <div className="h-1 w-12 rounded bg-[#787878]"></div>
                    <div className="h-1 w-12 rounded bg-[#787878]"></div>
                    <div className="h-1 w-12 rounded bg-[#9685FE]"></div>

                </div>
            </div>

            {/* Table */}
            <div className="overflow-hidden rounded-lg border border-[#F1F1F4] text-[13px] font-[400px]">
                <table className="w-full text-left border-collapse bg-[#232323]">
                    <thead className="bg-[#232323]">
                        <tr>
                            <th className="px-4 py-3 border border-[#F1F1F4]">CSV headers</th>
                            <th className="px-4 py-3 border border-[#F1F1F4]">Map with System Attributes (Optional)</th>
                            <th className="px-4 py-3 border border-[#F1F1F4]">Specify Default Values</th>
                            <th className="px-4 py-3 border border-[#F1F1F4]">Sample Input Data 1</th>
                            <th className="px-4 py-3 border border-[#F1F1F4]">Sample Input Data 2</th>
                        </tr>
                    </thead>
                    <tbody>
                        {[
                            { csv: "UUID", map: "UUID", default: "Candidate", s1: "CUST001", s2: "CUST002" },
                            { csv: "EMAIL", map: "Email", default: "Candidate", s1: "<EMAIL>", s2: "<EMAIL>" },
                            { csv: "MOBILE", map: "Mobile", default: "Candidate", s1: "9988776655", s2: "9988776655" },
                            { csv: "FIRSTNAME", map: "create", default: "Candidate", s1: "John Doe", s2: "John Doe" },
                        ].map((row, idx) => (
                            <tr key={idx}>
                                <td className="px-4 py-3 border border-[#F1F1F4]">{row.csv}</td>

                                <td
                                    className={`px-4 py-3 border border-[#F1F1F4] ${row.map === "create" ? "bg-[#101010]" : ""
                                        }`}
                                >
                                    {row.map === "create" ? (
                                        <button className="bg-[#5858B8] px-4 py-2 rounded-md text-sm w-full text-left text-white">
                                            create new attribute
                                        </button>
                                    ) : (
                                        <div className="flex justify-between items-center px-3 py-2 bg-[#3a3a3a] rounded-md cursor-pointer">
                                            <span>{row.map}</span>
                                            <ChevronDown size={16} className="text-white" />
                                        </div>
                                    )}
                                </td>


                                <td className="px-4 py-3 border border-[#F1F1F4]">{row.default}</td>
                                <td className="px-4 py-3 border border-[#F1F1F4]">{row.s1}</td>
                                <td className="px-4 py-3 border border-[#F1F1F4]">{row.s2}</td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>

            {/* Button */}
            <div className="fixed bottom-36 right-4 z-50">
                <button
                    className="px-7 py-2 w-40 bg-[#5858B8] border-2 border-[#53498c] text-white rounded-lg"
                    onClick={onNext}
                >
                    Next
                </button>
            </div>
        </div>
    );
}
