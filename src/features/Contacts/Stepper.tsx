import { faArrowLeft } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";


interface Step {
    icon: React.ReactNode;
    label: string;
}

interface StepperProps {
    step: number;
    onBack: () => void;
    title: string;
    subtitle?: string;
    steps?: Step[];
    variant?: "bulk" | "attribute";
    rightContent?: React.ReactNode;
}

export default function Stepper({
    step,
    onBack,
    title,
    subtitle,
    steps = [],
    variant = steps.length > 0 ? "bulk" : "attribute",
    rightContent,
}: StepperProps) {
    return (
        <div className="flex items-start justify-between w-full ">
            {/* Left: Back + title */}
            <div className="flex flex-col p-4">
                <div className="flex items-center gap-2">
                    <button
                        onClick={onBack}
                        className="text-white hover:text-white text-lg"
                    >
                        <FontAwesomeIcon icon={faArrowLeft} />
                    </button>
                    <h1 className="text-white text-xl font-semibold">{title}</h1>
                </div>
                {subtitle && <p className="text-sm text-white ml-6">{subtitle}</p>}
            </div>

            {/* Right side changes by variant */}
            {variant === "bulk" ? (
                <div className="flex items-center flex-1 ml-16 p-4">
                    {steps.map((s, index) => (
                        <div key={index} className="flex items-center gap-2">
                            <div
                                className={`size-12 rounded-full flex items-center justify-center 
                ${step >= index + 1
                                        ? "border-violet-500 bg-white/10"
                                        : "border-white/20 bg-white/5"
                                    } border`}
                            >
                                {s.icon}
                            </div>
                            <span className="text-white">{s.label}</span>

                            {index < steps.length - 1 && (
                                <div
                                    className={`w-40 border-t-2 border-dashed mx-6 
                  ${step >= index + 2 ? "border-violet-500" : "border-white/30"}`}
                                ></div>
                            )}
                        </div>
                    ))}
                </div>
            ) : (
                <div className="flex items-center gap-4 p-4">{rightContent}</div>
            )}
        </div>
    );
}
