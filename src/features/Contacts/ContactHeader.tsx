
import CustomModal from "@/components/custom/Modal/CustomModal";
import { Search, Plus, Filter, Trash2, Wrench } from "lucide-react";
import { useState } from "react";

import { useUploadStore } from "@/stores/useUploadstore";


export default function ContactsHeader() {

    const [open, setOpen] = useState(false);
    const { setMode } = useUploadStore();
    return (
        <>
            <div className="flex justify-between items-center mb-4 text-white p-4 rounded-lg">
                {/* Left side: Title */}
                <div>
                    <h1 className="text-2xl font-[700px]">All Contacts</h1>
                    <p className="text-white font-normal text-lg">View and manage contacts</p>
                </div>

                {/* Right side: Actions */}
                <div className="flex items-center gap-4">
                    <button className="p-2.5 bg-[#232323] rounded-lg hover:bg-gray-700">
                        <Search className="w-4 h-4" />
                    </button>
                    <button className="p-2.5 bg-[#232323] rounded-lg hover:bg-gray-700"
                        onClick={() => setOpen(true)}>
                        <Plus className="w-4 h-4" />
                    </button>
                    <button className="p-2.5 bg-[#232323] rounded-lg hover:bg-gray-700">
                        <Filter className="w-4 h-4" />
                    </button>
                    <button className="p-2.5 bg-[#232323] rounded-lg hover:bg-gray-700">
                        <Wrench className="w-4 h-4" />
                    </button>
                    <button className="p-2.5 bg-[#232323] rounded-lg hover:bg-gray-700">
                        <Trash2 className="w-4 h-4" />
                    </button>

                    <button
                        className="px-6.5 py-2 border-2 shadow border-[#53498c] text-white rounded-lg font-medium text-lg bg-[#5858B8]"
                        onClick={() => {
                            setMode("attribute");
                        }}
                    >
                        Attributes
                    </button>

                </div>
            </div>
            <CustomModal
                open={open}
                onOpenChange={setOpen}

            />

        </>
    );
}
