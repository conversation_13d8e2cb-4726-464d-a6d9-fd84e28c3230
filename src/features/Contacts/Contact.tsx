import { DataTable } from "@/components/custom/DataTable/DataTable";
import ContactsHeader from "./ContactHeader";
import { EllipsisVertical } from "lucide-react"; // shadcn icon
import BulkUpload from "./BulkModal/BulkUpload";
import { useUploadStore } from "@/stores/useUploadstore";
import texture from "@/assets/texture.svg"
import AttributionTable from "./AttributionTable";


export default function ContactsPage() {

    const { mode } = useUploadStore();
    const data = [
        {
            uuid: "Finalize Project Proposal",
            email: "<EMAIL>",
            mobile: "9087654321",
        },
        {
            uuid: "Conduct Client Meeting",
            email: "<EMAIL>",
            mobile: "9087654321",
        },
        {
            uuid: "QA Testing",
            email: "<EMAIL>",
            mobile: "9087654321",
        },
        {
            uuid: "Conduct Client Meeting",
            email: "<EMAIL>",
            mobile: "9087654321",
        },
        {
            uuid: "QA Testing",
            email: "<EMAIL>",
            mobile: "9087654321",
        },
        {
            uuid: "Conduct Client Meeting",
            email: "<EMAIL>",
            mobile: "9087654321",
        },
        {
            uuid: "QA Testing",
            email: "<EMAIL>",
            mobile: "9087654321",
        },
        {
            uuid: "Conduct Client Meeting",
            email: "<EMAIL>",
            mobile: "9087654321",
        },
        {
            uuid: "QA Testing",
            email: "<EMAIL>",
            mobile: "9087654321",
        },
    ];

    const columns = [
        { key: "uuid", title: "UUID (Primary Key)" },
        { key: "email", title: "Email" },
        { key: "mobile", title: "Mobile" },
        {
            key: "action",
            title: "Actions",
            render: () => (
                <button className="p-2 rounded ">
                    <EllipsisVertical className="w-5 h-5 text-white" />
                </button>
            ),
        },
    ];


    return (
        <div
            className="relative p-6 min-h-screen bg-cover bg-center"
            style={{ backgroundImage: `url(${texture})` }}
        >
            <div className="absolute inset-0 bg-black/50" />
            <div className="relative z-10">
                {mode === "bulk" ? (
                    <BulkUpload />
                ) : mode === "attribute" ? (
                    <AttributionTable />
                ) : (
                    <>
                        <ContactsHeader />
                        <DataTable data={data} columns={columns} />
                    </>
                )}
            </div>
        </div>
    );
}
