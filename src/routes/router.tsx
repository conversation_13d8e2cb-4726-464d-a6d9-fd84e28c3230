import { lazy, Suspense } from 'react';
import { createBrowserRouter } from 'react-router';
import { LoadingSpinner } from '../components/LoadingSpinner';
import Layout from '@/layouts/layout';


// import { ProtectedWrapper } from '../components/ProtectedWrapper';

// const Home = lazy(() => import('./pages/Home'));
// const Login = lazy(() => import('./pages/auth/Login'));
// const Overview = lazy(() => import('./pages/overview/Overview'));
// const WebFlow = lazy(() => import('./pages/flows/WebFlow'));
// const FlowEditor = lazy(() => import('./pages/flow-editor/FlowEditor'));
// const RootLayout = lazy(() => import('./components/RootLayout'));
const Contact = lazy(() => import('../features/Contacts/Contact'));

const router = createBrowserRouter([
  {
    path: "/",
    // element: (
    //   <Suspense fallback={<LoadingSpinner />}>
    //     {/* <RootLayout /> */}
    //   </Suspense>
    // ),
    children: [
      {
        path: "login",
        element: (
          <Suspense fallback={<LoadingSpinner />}>
            {/* <Login /> */}
          </Suspense>
        ),
      },
      //   {
      //     index: true,
      //     element: (
      //       <ProtectedWrapper>
      //         <Home />
      //       </ProtectedWrapper>
      //     ),
      //   },
      //   {
      //     path: "overview",
      //     element: (
      //       <ProtectedWrapper>
      //         <Overview />
      //       </ProtectedWrapper>
      //     ),
      //   },
      //   {
      //     path: "flows",
      //     element: (
      //       <ProtectedWrapper>
      //         <WebFlow />
      //       </ProtectedWrapper>
      //     ),
      //   },
      //   {
      //     path: "flow/:flowId",
      //     element: (
      //       <ProtectedWrapper>
      //         <FlowEditor />
      //       </ProtectedWrapper>
      //     ),
      //   }
      {
        path: "contact",
        element: (
          <Layout showTopBar showSideBar>
            <Contact />
          </Layout>

        ),
      },
      //        Page with all bars
      // <Layout showTopBar showSideBar>
      //     <Dashboard />
      // </Layout>
    ]
  }
])

export default router;