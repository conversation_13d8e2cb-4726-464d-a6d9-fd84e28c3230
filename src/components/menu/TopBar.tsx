import { useState } from "react";

export default function Topbar() {
    const [activeTab, setActiveTab] = useState("Data Store");

    const tabs = [
        "Home",
        "Agents",
        "Data Store",
        "Workflow",
        "Analytics",
        "Campaign Manager",
    ];

    return (
        <div className="flex justify-between bg-black px-4 border-b border-neutral-800">
            <div className="flex space-x-2">
                {tabs.map((tab) => (
                    <button
                        key={tab}
                        onClick={() => setActiveTab(tab)}
                        className={`pb-3 px-5 py-2 text-[14px] font-semibold ${activeTab === tab
                            ? "hover:text-white text-[#FFFFFF80] border-b-2"
                            : "text-[#FFFFFF80] hover:text-white"
                            }`}
                        style={activeTab === tab ? { borderColor: "#858BFE" } : {}}
                    >
                        {tab}
                    </button>
                ))}
            </div>
        </div>
    );
}
