
import Logo from "../../assets/Logo.svg"
import { faBell, faFire } from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";

export default function LogoBar() {
    return (
        <div className="flex items-center justify-between bg-black px-4 py-2 border-neutral-800">
            <div className="flex items-center gap-2">
                <img src={Logo} alt="Logo" />

            </div>
            <div className="flex items-center gap-4">
                <div className="relative flex items-center gap-1 px-4 py-2 rounded-lg text-sm text-white bg-neutral-900">
                    <FontAwesomeIcon icon={faFire} style={{ color: " #FF8A2A " }} />
                    <span>13 Active workflows</span>

                    {/* Top-left gradient border */}
                    <div className="absolute top-0 left-0 size-10 pointer-events-none rounded-tl-lg">
                        <div
                            className="w-full h-full rounded-tl-lg border-t-2 border-l-2"
                            style={{
                                borderColor: "transparent",
                                background:
                                    "linear-gradient(224deg, rgba(201, 211, 238, 0), #C9D3EE, rgba(201, 211, 238, 0)) border-box",
                                WebkitMask:
                                    "linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0)",
                                WebkitMaskComposite: "xor",
                                maskComposite: "exclude",
                            }}
                        ></div>
                    </div>

                    {/* Bottom-right gradient border */}
                    <div className="absolute bottom-0 right-0 size-10 pointer-events-none rounded-br-lg">
                        <div
                            className="w-full h-full rounded-br-lg border-b-2 border-r-2"
                            style={{
                                borderColor: "transparent",
                                background:
                                    "linear-gradient(224deg, rgba(201, 211, 238, 0), #C9D3EE, rgba(201, 211, 238, 0)) border-box",
                                WebkitMask:
                                    "linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0)",
                                WebkitMaskComposite: "xor",
                                maskComposite: "exclude",
                            }}
                        ></div>
                    </div>
                </div>

                <button className="corner-gradient relative p-2 hover:bg-neutral-800 text-white rounded-lg">
                    <FontAwesomeIcon icon={faBell} />
                    <div className="absolute top-0 left-0 size-6 pointer-events-none rounded-tl-lg">
                        <div
                            className="w-full h-full rounded-tl-lg border-t-2 border-l-2"
                            style={{
                                borderColor: "transparent",
                                background:
                                    "linear-gradient(224deg, rgba(201, 211, 238, 0), #C9D3EE, rgba(201, 211, 238, 0)) border-box",
                                WebkitMask:
                                    "linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0)",
                                WebkitMaskComposite: "xor",
                                maskComposite: "exclude",
                            }}
                        ></div>
                    </div>
                    <div className="absolute bottom-0 right-0 size-6 pointer-events-none rounded-br-lg">
                        <div
                            className="w-full h-full rounded-br-lg border-b-2 border-r-2"
                            style={{
                                borderColor: "transparent",
                                background:
                                    "linear-gradient(224deg, rgba(201, 211, 238, 0), #C9D3EE, rgba(201, 211, 238, 0)) border-box",
                                WebkitMask:
                                    "linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0)",
                                WebkitMaskComposite: "xor",
                                maskComposite: "exclude",
                            }}
                        ></div>
                    </div>
                </button>
            </div>
        </div>
    );
}
