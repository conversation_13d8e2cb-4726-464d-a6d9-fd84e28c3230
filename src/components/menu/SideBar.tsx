import { Calendar, Layers, List } from "lucide-react";
import { SquaresFour, Users, CaretDown, CaretUp } from "phosphor-react";
import { useState } from "react";
import { Link } from "react-router-dom";

export default function Sidebar() {
    const [openMenus, setOpenMenus] = useState<{ [key: string]: boolean }>({
        contacts: false,
        events: false,
    });

    function toggleMenu(menu: string) {
        setOpenMenus((prev) => ({
            ...prev,
            [menu]: !prev[menu],
        }));
    }

    const contactsSubmenu = [
        { name: "All Contacts", path: "/contact" },
        { name: "Segments", path: "/segments" },
    ];

    const eventsSubmenu = [
        { name: "All Events", path: "/events" },
        { name: "Event Types", path: "/event-types" },
    ];

    return (
        <div className="w-64 bg-black border-r border-neutral-900 text-[#FFFFFF80] flex flex-col p-3 select-none text-[14px] font-medium">
            {/* Overview */}
            <div className="flex items-center gap-2 px-3 py-2 rounded-md cursor-pointer hover:bg-neutral-800 group transition-colors duration-200">
                <SquaresFour className="w-4 h-4 text-[#FFFFFF80] group-hover:text-white" />
                <span className="group-hover:text-white">Overview</span>
            </div>

            <nav className="flex flex-col gap-1 text-sm mt-2">
                {/* Contacts */}
                <div
                    onClick={() => toggleMenu("contacts")}
                    className="flex items-center justify-between px-3 py-2 rounded-md cursor-pointer hover:bg-neutral-800 group transition-colors duration-200"
                >
                    <div className="flex items-center gap-2">
                        <Users className="w-4 h-4 text-[#FFFFFF80] group-hover:text-white" />
                        <span className="group-hover:text-white">Contacts</span>
                    </div>
                    {openMenus.contacts ? (
                        <CaretUp size={16} weight="bold" className="text-[#FFFFFF80] group-hover:text-white" />
                    ) : (
                        <CaretDown size={16} weight="bold" className="text-[#FFFFFF80] group-hover:text-white" />
                    )}
                </div>

                {openMenus.contacts &&
                    contactsSubmenu.map((item) => (
                        <Link
                            to={item.path}
                            key={item.name}
                            className="ml-8 px-3 py-1 rounded-md cursor-pointer hover:bg-neutral-800 group flex items-center gap-2 transition-colors duration-200"
                        >
                            <span className="text-[#FFFFFF80] group-hover:text-[#858BFE]">{item.name}</span>
                        </Link>
                    ))}

                {/* Events */}
                <div
                    onClick={() => toggleMenu("events")}
                    className="flex items-center justify-between px-3 py-2 rounded-md cursor-pointer hover:bg-neutral-800 group transition-colors duration-200"
                >
                    <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-[#FFFFFF80] group-hover:text-white" />
                        <span className="group-hover:text-white">Events</span>
                    </div>
                    {openMenus.events ? (
                        <CaretUp size={16} weight="bold" className="text-[#FFFFFF80] group-hover:text-white" />
                    ) : (
                        <CaretDown size={16} weight="bold" className="text-[#FFFFFF80] group-hover:text-white" />
                    )}
                </div>

                {openMenus.events &&
                    eventsSubmenu.map((item) => (
                        <Link
                            to={item.path}
                            key={item.name}
                            className="ml-8 px-3 py-1 rounded-md cursor-pointer hover:bg-neutral-800 group flex items-center gap-2 transition-colors duration-200"
                        >
                            <span className="text-[#FFFFFF80] group-hover:text-[#858BFE]">{item.name}</span>
                        </Link>
                    ))}

                {/* Schemas */}
                <div className="flex items-center gap-2 px-3 py-2 rounded-md cursor-pointer hover:bg-neutral-800 group transition-colors duration-200">
                    <Layers className="w-4 h-4 text-[#FFFFFF80] group-hover:text-white" />
                    <span className="group-hover:text-white">Schemas</span>
                </div>

                {/* Logs */}
                <div className="flex items-center gap-2 px-3 py-2 rounded-md cursor-pointer hover:bg-neutral-800 group transition-colors duration-200">
                    <List className="w-4 h-4 text-[#FFFFFF80] group-hover:text-white" />
                    <span className="group-hover:text-white">Logs</span>
                </div>
            </nav>
        </div>
    );
}
