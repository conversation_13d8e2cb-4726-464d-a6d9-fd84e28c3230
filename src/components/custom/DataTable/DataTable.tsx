import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../ui/table';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import type { DataTableProps } from './DataTable.types';
import { cn } from '../../../lib/utils';

export function DataTable<T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  onRowClick,
  searchable = false,
  searchPlaceholder = "Search...",
  emptyMessage = 'No data available',
  className,
}: DataTableProps<T>) {
  const [searchTerm, setSearchTerm] = React.useState('');

  const filteredData = React.useMemo(() => {
    if (!searchable || !searchTerm) return data;

    return data.filter((item) =>
      Object.values(item).some((value) =>
        String(value).toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [data, searchTerm, searchable]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2">Loading...</span>
      </div>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {searchable && (
        <Input
          placeholder={searchPlaceholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
      )}

      {filteredData.length === 0 ? (
        <Alert>
          <AlertDescription>{emptyMessage}</AlertDescription>
        </Alert>
      ) : (
        <div className="relative">
          <div className="absolute top-0 left-0 size-60 pointer-events-none rounded-tl-3xl">
            <div
              className="w-full h-full rounded-tl-3xl border-t-2 border-l-2"
              style={{
                borderColor: "transparent",
                background:
                  "linear-gradient(224deg, rgba(201, 211, 238, 0), #C9D3EE, rgba(201, 211, 238, 0)) border-box",
                WebkitMask:
                  "linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0)",
                WebkitMaskComposite: "xor",
                maskComposite: "exclude",
              }}
            ></div>
          </div>

          {/* Gradient stroke bottom-right */}
          <div className="absolute bottom-0 right-0 size-60 pointer-events-none rounded-br-3xl">
            <div
              className="w-full h-full rounded-br-3xl border-b-2 border-r-2"
              style={{
                borderColor: "transparent",
                background:
                  "linear-gradient(224deg, rgba(201, 211, 238, 0), #C9D3EE, rgba(201, 211, 238, 0)) border-box",
                WebkitMask:
                  "linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0)",
                WebkitMaskComposite: "xor",
                maskComposite: "exclude",
              }}
            ></div>
          </div>

          <div className="overflow-hidden rounded-3xl border border-transparent bg-gradient-to-b from-[#101010CC] to-[#00000033] p-4">
            <Table>
              <TableHeader >
                <TableRow className="border-none">
                  {columns.map((column) => (
                    <TableHead
                      key={column.key as string}
                      className={cn("text-gray-400 font-medium bg-[#1D1D1D] ", column.className)}
                      style={{ width: column.width }}
                      title={String(column.title)}
                    >
                      {column.title}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredData.map((row, index) => (
                  <TableRow
                    key={index}
                    className={onRowClick ? "cursor-pointer" : "border-none"}
                    onClick={() => onRowClick?.(row)}
                  >
                    {columns.map((column) => (
                      <TableCell
                        key={column.key as string}
                        className="text-gray-300 border-none"
                      >
                        {column.render
                          ? column.render(row[column.key], row)
                          : row[column.key]}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      )}
    </div>
  );
}
