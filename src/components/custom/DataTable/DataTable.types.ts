import type { ReactNode } from "react";

export interface Column<T> {
  key: string; //keyof T
  title: string;
  width?: string;
  className?: string;
  render?: (value: any, record: T) => ReactNode;
}

export interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  onRowClick?: (record: T) => void;
  searchable?: boolean;
  searchPlaceholder?: string;
  emptyMessage?: string;
  className?: string;
}
