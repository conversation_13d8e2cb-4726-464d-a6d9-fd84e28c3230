import { useState } from "react";
import {
    <PERSON><PERSON>,
    Dialog<PERSON>ontent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogOverlay,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import excess from "@/assets/excess.svg";
import { useUploadStore } from "@/stores/useUploadstore";

interface CustomModalProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onBulkUpload?: () => void;
    onSingleUpload?: () => void;
}

export default function CustomModal({
    open,
    onOpenChange,

}: CustomModalProps) {
    const [selected, setSelected] = useState<"bulk" | "single" | null>(null);
    const { setMode } = useUploadStore();
    function handleNext() {
        if (selected === "bulk") setMode("bulk");
        if (selected === "single") setMode("single");
        onOpenChange(false);
    }

    return (
        <Dialog open={open} onOpenChange={onO<PERSON><PERSON><PERSON><PERSON>}>
            <DialogOverlay className="fixed inset-0 bg-black/60 backdrop-blur-sm" />
            <DialogContent className="sm:max-w-[800px] h-[672px] rounded-2xl bg-[#232323] border border-neutral-800 p-8 shadow-2xl flex flex-col items-center justify-center">

                {/* Big centered image */}
                <img
                    src={excess}
                    alt="Icon"
                    className="w-48 h-36 object-contain mb-4"
                />

                <DialogHeader className="text-center">
                    <DialogTitle className="text-2xl font-medium text-white">
                        How would you like to add contacts?
                    </DialogTitle>
                    <DialogDescription className="text-[#FFFFFF80] text-center text-[16px]">
                        Learn how to add contacts
                    </DialogDescription>
                </DialogHeader>

                <div className="mt-6 flex flex-col items-center gap-4 sm:flex-row sm:justify-center">
                    {/* Bulk Upload */}
                    <label
                        className={`flex items-center gap-4 px-5 py-2 rounded-xl border-2 transition-shadow duration-150 cursor-pointer
        ${selected === "bulk"
                                ? "border-[#858BFE] bg-[#727DA144] shadow-md"
                                : "border-neutral-700 bg-transparent hover:border-[#6F66D9]"
                            }`}
                    >
                        <input
                            type="checkbox"
                            checked={selected === "bulk"}
                            onChange={() => setSelected(selected === "bulk" ? null : "bulk")}
                            className="hidden"
                        />
                        <span
                            className={`w-5 h-5 inline-flex items-center justify-center rounded-sm border-2 ${selected === "bulk" ? "bg-[#858BFE] border-[#858BFE]" : "border-neutral-600"
                                }`}
                        />
                        <span className="text-white text-lg">Bulk upload</span>
                    </label>

                    {/* Single Contact */}
                    <label
                        className={`flex items-center gap-4 px-5 py-2 rounded-xl border-2 transition-shadow duration-150 cursor-pointer
        ${selected === "single"
                                ? "border-[#858BFE] bg-[#727DA144] shadow-md"
                                : "border-neutral-700 bg-transparent hover:border-[#6F66D9]"
                            }`}
                    >
                        <input
                            type="checkbox"
                            checked={selected === "single"}
                            onChange={() => setSelected(selected === "single" ? null : "single")}
                            className="hidden"
                        />
                        <span
                            className={`w-5 h-5 inline-flex items-center justify-center rounded-sm border-2 ${selected === "single" ? "bg-[#858BFE] border-[#858BFE]" : "border-neutral-600"
                                }`}
                        />
                        <span className="text-white text-lg">Single contact</span>
                    </label>
                </div>

                <div className="mt-2 flex justify-center">
                    <Button
                        onClick={handleNext}
                        className="w-48 rounded-lg px-6.5 py-2.5 bg-[#5858B8] border-2 border-[#53498c]  shadow-md text-lg font-medium"
                    >
                        Next
                    </Button>
                </div>

                <div className="mt-8 text-left text-white w-full max-w-[700px] mx-auto">
                    <h3 className="text-lg font-[400px] text-white mb-3">
                        Ensure the following conditions are met:
                    </h3>
                    <ul className="list-disc list-inside  font-[400px] space-y-3 text-sm text-[#FFFFFF80] ">
                        <li>You have permission to send communications to all uploaded contacts.</li>
                        <li>You have not purchased or rented the contact data.</li>
                        <li>The contact data excludes distribution lists or group email addresses.</li>
                        <li>The contact data matches required data types; unmatched fields will be left blank.</li>
                        <li>You agree to the platform's anti-spam policy, acknowledging violations may result in account termination.</li>
                    </ul>
                </div>
            </DialogContent>
        </Dialog>
    );
}
